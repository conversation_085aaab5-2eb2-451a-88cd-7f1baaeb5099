# Confluence MCP Server Configuration

# Required: Confluence API Token
# Get from: https://id.atlassian.com/manage/api-tokens
CONFLUENCE_API_TOKEN=your_api_token_here

# Required: Confluence Base URL
# Format: https://your-domain.atlassian.net/wiki
CONFLUENCE_BASE_URL=https://your-domain.atlassian.net/wiki

# Optional: Read-only mode (default: false)
# When set to true, only allows read operations and adding comments
# Blocks create_page, update_page, and add_attachment operations
CONFLUENCE_READ_ONLY_MODE=false

# Optional: MCP Transport Method
# Options: stdio (default), sse, streamable-http
MCP_TRANSPORT=stdio

# Optional: HTTP Server Configuration (for sse and streamable-http only)
# Default port: 3000
MCP_PORT=3000

# Default host: localhost
MCP_HOST=localhost
