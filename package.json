{"name": "confluence-mcp", "version": "1.0.1", "description": "Confluence MCP Server", "module": "index.ts", "type": "module", "scripts": {"build-unix": "vite build && chmod +x dist/index.js && chmod +x dist/index-multi.js", "build": "vite build", "build:dxt": "npm run build && node scripts/build-dxt.js", "test:dxt": "node scripts/test-dxt.js", "test:dxt:manual": "node scripts/test-dxt-manual.js", "demo:dxt": "node scripts/demo-dxt-execution.js", "diagnose": "node scripts/diagnose-mcp.js", "start": "bun dist/index.js", "start:multi": "bun dist/index-multi.js", "start:sse": "MCP_TRANSPORT=sse bun dist/index-multi.js", "start:http": "MCP_TRANSPORT=streamable-http bun dist/index-multi.js", "dev": "bun --watch src/index.ts", "dev:multi": "bun --watch src/index-multi.ts", "test": "bun test", "test:watch": "bun test --watch", "prepare": "husky"}, "keywords": ["mcp", "confluence", "typescript", "bun"], "author": "<PERSON><PERSON>", "license": "MIT", "devDependencies": {"@types/bun": "^1.2.13", "@types/express": "^5.0.3", "@types/node": "^22.15.19", "husky": "^9.1.7", "lint-staged": "^16.0.0", "prettier": "^3.5.3", "vite": "^6.3.5", "vite-plugin-dts": "^4.5.4", "vitest": "^3.1.4"}, "peerDependencies": {"typescript": "^5.8.3"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.11.4", "@types/sanitize-html": "^2.16.0", "dotenv": "^17.1.0", "express": "^5.1.0", "sanitize-html": "^2.17.0"}, "lint-staged": {"*.{js,jsx,ts,tsx,json,css,md}": "prettier --write"}}